<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Filters\Tenant\PayslipFilter;
use App\Helpers\Traits\ConflictedPayslipQueryHelpers;
use App\Helpers\Traits\DateRangeHelper;
use App\Http\Controllers\Controller;
use App\Models\Core\Auth\User;
use App\Models\Tenant\Payroll\Payslip;
use App\Repositories\Core\Status\StatusRepository;
use App\Services\Tenant\Payroll\EmployeePayrunService;
use App\Services\Tenant\Payroll\PayslipService;
use App\Services\Tenant\Setting\SettingService as TenantSettingService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class PayrollSummeryController extends Controller
{
    use DateRangeHelper, ConflictedPayslipQueryHelpers;

    public function __construct(PayslipService $service, PayslipFilter $filter)
    {
        $this->service = $service;
        $this->filter = $filter;
    }

    public function index(User $employee)
    {
        $this->authorized($employee);

        $within = request()->get('within');
        $month = $within ?: request('month_number') + 1;
        $ranges = $this->convertRangesToStringFormat($this->getStartAndEndOf($month, request()->get('year')));

        if ($within == 'total') {
            $min_date = Payslip::query()->oldest('start_date')->first()?->start_date;
            $ranges = [$min_date ?: todayFromApp()->toDateString(), todayFromApp()->toDateString()];
        }

        if (request()->get('date_range')) {
            $date = json_decode(htmlspecialchars_decode(request()->get('date_range')), true);
            $ranges = [Carbon::parse($date['start']), Carbon::parse($date['end'])];
        }



        $payslips = Payslip::query()
            ->where('user_id', $employee->id)
            ->with($this->service->getRelations())
            ->whereBetween(DB::raw('DATE(start_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->whereBetween(DB::raw('DATE(end_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->latest()
            ->paginate(request()->get('per_page', 12));

        $payslips->map(function ($payslip){
            $conflictedData = $this->conflictedUserPayslip($payslip, $payslip->start_date, $payslip->end_date);
            $payslip->conflicted = $conflictedData->count();
        });

        return $payslips;
    }

    public function summery(User $employee)
    {
        $this->authorized($employee);

        $within = request()->get('within');
        $month = $within ?: request('month_number') + 1;
        $ranges = $this->convertRangesToStringFormat($this->getStartAndEndOf($month, request()->get('year')));

        if ($within == 'total') {
            $min_date = Payslip::query()->oldest('start_date')->first()?->start_date;
            $ranges = [$min_date ?: todayFromApp()->toDateString(), todayFromApp()->toDateString()];
        }

        $statusSent = resolve(StatusRepository::class)->payslipSent();
        $statusPending = resolve(StatusRepository::class)->payslipPending();

        $totalPayslipQuery = $employee->payslips()
            ->whereBetween(DB::raw('DATE(start_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->whereBetween(DB::raw('DATE(end_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges);

        $totalPayslipQueryClone = clone $totalPayslipQuery;
        $totalConflict = $totalPayslipQueryClone->whereIn('id', $this->getConflictedPayslip())->count();

        $totalPayslip = $totalPayslipQuery->get();

        $payslipSent = $totalPayslip->where('status_id', $statusSent);

        $payslipPending = $totalPayslip->where('status_id', $statusPending);

        $employee->load([
            'department:id,name',
            'profile',
            'profilePicture',
        ]);
        return [
            'card_summaries' => [
                'total' => $totalPayslip->count(),
                'sent' => $payslipSent->count(),
                'pending' => $payslipPending->count(),
                'conflicted' => $totalConflict,
            ],
            'employee' => $employee
        ];
    }

    public function allEmployeesSummary()
    {
        $within = request()->get('within');
        $month = $within ?: request('month_number') + 1;
        $ranges = $this->convertRangesToStringFormat($this->getStartAndEndOf($month, request()->get('year')));

        if ($within == 'total') {
            $min_date = Payslip::query()->oldest('start_date')->first()?->start_date;
            $ranges = [$min_date ?: todayFromApp()->toDateString(), todayFromApp()->toDateString()];
        }

        if (request()->get('date_range')) {
            $date = json_decode(htmlspecialchars_decode(request()->get('date_range')), true);
            $ranges = [Carbon::parse($date['start']), Carbon::parse($date['end'])];
        }

        $statusSent = resolve(StatusRepository::class)->payslipSent();
        $statusPending = resolve(StatusRepository::class)->payslipPending();

        // Get all payslips for all employees within the date range
        $totalPayslipQuery = Payslip::query()
            ->whereBetween(DB::raw('DATE(start_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->whereBetween(DB::raw('DATE(end_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges);

        $totalPayslipQueryClone = clone $totalPayslipQuery;
        $totalConflict = $totalPayslipQueryClone->whereIn('id', $this->getConflictedPayslip())->count();

        $totalPayslip = $totalPayslipQuery->get();

        $payslipSent = $totalPayslip->where('status_id', $statusSent);
        $payslipPending = $totalPayslip->where('status_id', $statusPending);

        // Get unique employees count
        $uniqueEmployeesCount = $totalPayslip->pluck('user_id')->unique()->count();

        return [
            'card_summaries' => [
                'total' => $totalPayslip->count(),
                'sent' => $payslipSent->count(),
                'pending' => $payslipPending->count(),
                'conflicted' => $totalConflict,
                'employees' => $uniqueEmployeesCount,
            ],
            'date_range' => $ranges,
            'period_text' => $this->formatDateRange($ranges),
            'is_all_employees' => true
        ];
    }

    public function allEmployeesIndex()
    {
        $within = request()->get('within');
        $month = $within ?: request('month_number') + 1;
        $ranges = $this->convertRangesToStringFormat($this->getStartAndEndOf($month, request()->get('year')));

        if ($within == 'total') {
            $min_date = Payslip::query()->oldest('start_date')->first()?->start_date;
            $ranges = [$min_date ?: todayFromApp()->toDateString(), todayFromApp()->toDateString()];
        }

        if (request()->get('date_range')) {
            $date = json_decode(htmlspecialchars_decode(request()->get('date_range')), true);
            $ranges = [Carbon::parse($date['start']), Carbon::parse($date['end'])];
        }

        $payslips = Payslip::query()
            ->with($this->service->getRelations())
            ->whereBetween(DB::raw('DATE(start_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->whereBetween(DB::raw('DATE(end_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->latest()
            ->paginate(request()->get('per_page', 12));

        $payslips->map(function ($payslip){
            $conflictedData = $this->conflictedUserPayslip($payslip, $payslip->start_date, $payslip->end_date);
            $payslip->conflicted = $conflictedData->count();
        });

        return $payslips;
    }

    private function formatDateRange($ranges)
    {
        if (count($ranges) == 2) {
            $startFormatted = Carbon::parse($ranges[0])->format('M d');
            $endFormatted = Carbon::parse($ranges[1])->format('M d, Y');
            return strtoupper($startFormatted . '-' . $endFormatted);
        }
        return 'N/A';
    }

    public function authorized(User $employee): self
    {
        resolve(EmployeePayrunService::class)
            ->setModel($employee)
            ->validateForEmployeesPayslipsAccess(request()->get('show_all'));

        return $this;
    }



    public function exportAllEmployeesTransmittalPdf()
    {
        $within = request()->get('within');
        $month = $within ?: request('month_number') + 1;
        $ranges = $this->convertRangesToStringFormat($this->getStartAndEndOf($month, request()->get('year')));

        if ($within == 'total') {
            $min_date = Payslip::query()->oldest('start_date')->first()?->start_date;
            $ranges = [$min_date ?: todayFromApp()->toDateString(), todayFromApp()->toDateString()];
        }

        if (request()->get('date_range')) {
            $date = json_decode(htmlspecialchars_decode(request()->get('date_range')), true);
            $ranges = [Carbon::parse($date['start']), Carbon::parse($date['end'])];
        }

        $payslips = $this->service
            ->filters($this->filter)
            ->with($this->service->getRelations())
            ->whereBetween(DB::raw('DATE(start_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->whereBetween(DB::raw('DATE(end_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->latest()
            ->get();

        $settings = (object)resolve(TenantSettingService::class)
            ->getFormattedTenantSettings();

        $pdf = PDF::loadView('tenant.payroll.pdf.all-employees-transmittal', compact('payslips', 'settings', 'ranges'));
        $fileName = 'All-Employees-Transmittal-' . now()->format('Y-m-d') . '.pdf';

        return $pdf->stream($fileName);
    }

    public function exportAllEmployeesSignaturePdf()
    {
        $within = request()->get('within');
        $month = $within ?: request('month_number') + 1;
        $ranges = $this->convertRangesToStringFormat($this->getStartAndEndOf($month, request()->get('year')));

        if ($within == 'total') {
            $min_date = Payslip::query()->oldest('start_date')->first()?->start_date;
            $ranges = [$min_date ?: todayFromApp()->toDateString(), todayFromApp()->toDateString()];
        }

        if (request()->get('date_range')) {
            $date = json_decode(htmlspecialchars_decode(request()->get('date_range')), true);
            $ranges = [Carbon::parse($date['start']), Carbon::parse($date['end'])];
        }

        $payslips = $this->service
            ->filters($this->filter)
            ->with($this->service->getRelations())
            ->whereBetween(DB::raw('DATE(start_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->whereBetween(DB::raw('DATE(end_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->latest()
            ->get();

        $settings = (object)resolve(TenantSettingService::class)
            ->getFormattedTenantSettings();

        $pdf = PDF::loadView('tenant.payroll.pdf.all-employees-signature', compact('payslips', 'settings', 'ranges'));
        $fileName = 'All-Employees-For-Signature-' . now()->format('Y-m-d') . '.pdf';

        return $pdf->stream($fileName);
    }

    public function exportAllEmployeesConsolidationPdf()
    {
        $within = request()->get('within');
        $month = $within ?: request('month_number') + 1;
        $ranges = $this->convertRangesToStringFormat($this->getStartAndEndOf($month, request()->get('year')));

        if ($within == 'total') {
            $min_date = Payslip::query()->oldest('start_date')->first()?->start_date;
            $ranges = [$min_date ?: todayFromApp()->toDateString(), todayFromApp()->toDateString()];
        }

        if (request()->get('date_range')) {
            $date = json_decode(htmlspecialchars_decode(request()->get('date_range')), true);
            $ranges = [Carbon::parse($date['start']), Carbon::parse($date['end'])];
        }

        $payslips = $this->service
            ->filters($this->filter)
            ->with($this->service->getRelations())
            ->whereBetween(DB::raw('DATE(start_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->whereBetween(DB::raw('DATE(end_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->latest()
            ->get();

        $settings = (object)resolve(TenantSettingService::class)
            ->getFormattedTenantSettings();

        $pdf = PDF::loadView('tenant.payroll.pdf.all-employees-consolidation', compact('payslips', 'settings', 'ranges'));
        $fileName = 'All-Employees-Consolidation-' . now()->format('Y-m-d') . '.pdf';

        return $pdf->stream($fileName);
    }

    public function exportAllEmployeesSummaryPdf()
    {
        $within = request()->get('within');
        $month = $within ?: request('month_number') + 1;
        $ranges = $this->convertRangesToStringFormat($this->getStartAndEndOf($month, request()->get('year')));

        if ($within == 'total') {
            $min_date = Payslip::query()->oldest('start_date')->first()?->start_date;
            $ranges = [$min_date ?: todayFromApp()->toDateString(), todayFromApp()->toDateString()];
        }

        if (request()->get('date_range')) {
            $date = json_decode(htmlspecialchars_decode(request()->get('date_range')), true);
            $ranges = [Carbon::parse($date['start']), Carbon::parse($date['end'])];
        }

        $payslips = $this->service
            ->filters($this->filter)
            ->with($this->service->getRelations())
            ->whereBetween(DB::raw('DATE(start_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->whereBetween(DB::raw('DATE(end_date)'), count($ranges) == 1 ? [$ranges[0], $ranges[0]] : $ranges)
            ->latest()
            ->get();

        $settings = (object)resolve(TenantSettingService::class)
            ->getFormattedTenantSettings();

        $pdf = PDF::loadView('tenant.payroll.pdf.all-employees-summary', compact('payslips', 'settings', 'ranges'));
        $fileName = 'All-Employees-Summary-' . now()->format('Y-m-d') . '.pdf';

        return $pdf->stream($fileName);
    }
}
