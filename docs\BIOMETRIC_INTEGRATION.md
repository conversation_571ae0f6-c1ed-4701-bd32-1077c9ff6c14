# Biometric Device Integration

This document explains how the HRIS system integrates with multiple ZKTeco biometric devices for automatic attendance tracking.

## Overview

The BiometricController handles data from multiple ZKTeco biometric devices and automatically creates attendance records in the system. The system supports different device modes (automatic, punch in only, punch out only) and can manage multiple devices simultaneously. When employees punch in/out on any configured biometric device, the data is sent to the HRIS system and processed to create attendance records.

## How It Works

### 1. Device Communication
- ZKTeco devices send attendance data to the `/iclock/cdata` endpoint
- Data is sent in ATTLOG format: `ATTLOG\t{user_id}\t{timestamp}\t{check_type}\t{verify_mode}`

### 2. Data Processing
The system processes the biometric data as follows:

1. **Parse ATTLOG Data**: Extract user ID, timestamp, check type, and verification mode
2. **Find User**: Match the biometric user ID with the employee_id in the user's profile
3. **Determine Action**: Check if it's a punch in (check_type = 0) or punch out (check_type = 1)
4. **Create Attendance**: Use the AttendanceService to create proper attendance records

### 3. Employee Mapping
- Biometric device user IDs are mapped to the `employee_id` field in the user's profile
- The system supports both direct matching and automatic EMP- prefix mapping:
  - If biometric device sends "1", system first tries to find employee_id = "1"
  - If not found and ID is numeric, tries to find employee_id = "EMP-1"
- This flexible mapping accommodates different employee ID formats

## Configuration

### 1. Employee Setup
Ensure each employee has their `employee_id` set in their profile. You can use either format:

**Option 1: EMP- prefix format (recommended)**
```php
$user->profile()->update([
    'employee_id' => 'EMP-1' // Biometric device user ID: 1
]);
```

**Option 2: Direct numeric format**
```php
$user->profile()->update([
    'employee_id' => '1' // Biometric device user ID: 1
]);
```

The system will automatically handle the mapping between numeric biometric IDs and EMP- prefixed employee IDs.

### 2. Device Configuration
Configure your ZKTeco devices to send data to:
- URL: `http://your-domain.com/iclock/cdata?device_id=YOUR_DEVICE_ID`
- Method: POST
- Format: ATTLOG
- Include device ID in query parameter or custom header for device identification

### 3. Device Management
Access the biometric device management through:
- Navigate to **Settings > Attendance Settings > Biometric Settings**
- Enable biometric device integration
- Add and configure multiple devices with different modes:
  - **Automatic**: Handles both punch in and punch out
  - **Punch In Only**: Only processes punch in events
  - **Punch Out Only**: Only processes punch out events

## Data Flow

```
Multiple Biometric Devices → /iclock/cdata → Device Identification → Mode Validation → AttendanceService → Database
```

### Device Identification Methods
1. **Query Parameter**: `?device_id=ZK001`
2. **Custom Header**: `X-Device-ID: ZK001`
3. **User Agent Parsing**: Extract device ID from user agent string
4. **IP Address Mapping**: Configure IP to device mapping (fallback)

### Example ATTLOG Data
```
ATTLOG	1	2024-01-15 09:00:00	0	1
```
- `1`: Biometric user ID (maps to profile.employee_id "1" or "EMP-1")
- `2024-01-15 09:00:00`: Timestamp
- `0`: Check type (0=Check-In, 1=Check-Out)
- `1`: Verification mode

## Features

### Automatic Punch Detection
If check_type is not provided, the system automatically determines if it's a punch in or out:
- If user has no unpunched out record for today → Punch In
- If user has an unpunched out record → Punch Out

### Error Handling
- Unknown employee IDs are logged as warnings
- Database errors are caught and logged
- Invalid data formats are handled gracefully

### Logging
All biometric activities are logged for debugging and audit purposes:
- Raw data received from device
- Parsed attendance data
- Successful punch in/out events
- Errors and warnings

## Database Records

### Attendance Table
- `user_id`: The employee who punched in/out
- `in_date`: Date of attendance
- `status_id`: Approved status (automatically set)
- `working_shift_id`: Employee's working shift
- `behavior`: Early/Late/Regular (calculated automatically)

### Attendance Details Table
- `in_time`: Punch in timestamp
- `out_time`: Punch out timestamp (null for punch in only)
- `attendance_id`: Reference to attendance record
- `status_id`: Approved status
- `in_ip_data`: IP and device information (JSON)
- `out_ip_data`: IP and device information for punch out (JSON)

## IP Data Structure
The system stores additional metadata about the biometric punch:
```json
{
    "ip": "*************",
    "source": "Biometric Device",
    "verify_mode": "1"
}
```

## Testing

Run the biometric integration tests:
```bash
php artisan test tests/Feature/BiometricControllerTest.php
```

## Troubleshooting

### Common Issues

1. **Employee not found**
   - Ensure `employee_id` in profile matches biometric device user ID
   - Check logs for "User with biometric ID {id} not found" warnings

2. **Attendance not created**
   - Verify device is sending data to correct endpoint
   - Check application logs for errors
   - Ensure database has proper working shifts and statuses

3. **Duplicate punch records**
   - System automatically handles duplicate detection
   - Check if device is sending duplicate data

### Debug Mode
Enable detailed logging by setting log level to debug in your `.env`:
```
LOG_LEVEL=debug
```

## Security Considerations

- Biometric endpoint accepts data from any source
- Consider implementing device authentication if needed
- IP restrictions can be added for additional security
- All attendance data is logged for audit trails
