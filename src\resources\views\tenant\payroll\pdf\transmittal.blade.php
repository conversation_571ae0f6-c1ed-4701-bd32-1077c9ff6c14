<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Transmittal PDF</title>
    <link rel="stylesheet" href="{{ url('css/payslip.css') }}">
    <style>
        .transmittal-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .transmittal-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .transmittal-table th,
        .transmittal-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: middle;
        }
        .transmittal-table th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }
        .transmittal-table td {
            font-size: 11px;
        }
        .employee-info {
            margin-bottom: 20px;
        }
        .summary-section {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
<div>
    <div class="transmittal-header">
        <img src="{{ property_exists($settings, 'tenant_logo') ? asset($settings->tenant_logo) : asset('images/logo/default-logo.png') }}"
             alt="logo"
             class="img-fluid mb-2"
             style="max-height: 100px; max-width: 150px;"
        />
        <h3 class="font-weight-bold">{{ $settings->tenant_name ?? 'Company Name' }}</h3>
        @if(property_exists($settings, 'address') || property_exists($settings, 'area') || property_exists($settings, 'city'))
            <p class="mb-2">
                {{ property_exists($settings, 'address') ? $settings->address : '' }}{{ property_exists($settings, 'address') && (property_exists($settings, 'area') || property_exists($settings, 'city') || property_exists($settings, 'zip_code') || property_exists($settings, 'country')) ? '' : '' }}
            </p>
        @endif
        <h4>PAYROLL TRANSMITTAL</h4>
        <p>Generated on: {{ now()->format('F d, Y') }}</p>
    </div>

    @php
        // Get unique employees from payslips
        $uniqueEmployees = collect();
        $totalSalary = 0;

        // Get payroll period from payslips
        $payrollPeriodText = 'N/A';
        if ($payslips->isNotEmpty()) {
            $startDate = $payslips->min('start_date');
            $endDate = $payslips->max('end_date');

            if ($startDate && $endDate) {
                $startFormatted = \Carbon\Carbon::parse($startDate)->format('M d');
                $endFormatted = \Carbon\Carbon::parse($endDate)->format('M d, Y');
                $payrollPeriodText = strtoupper($startFormatted . '-' . $endFormatted);
            }
        }

        foreach($payslips as $payslip) {
            if (!$uniqueEmployees->contains('id', $payslip->user->id)) {
                // Load profile and department relationships if not already loaded
                $payslip->user->load(['profile', 'department', 'designation']);
                $uniqueEmployees->push($payslip->user);
            }
        }

        // If no payslips, use the single employee
        if ($uniqueEmployees->isEmpty() && isset($employee)) {
            $uniqueEmployees->push($employee);
        }
    @endphp

    <div class="payroll-info">
        <h5>PAYROLL COVERED AS OF {{ $payrollPeriodText }}</h5>
    </div>

    <table class="transmittal-table">
        <thead>
            <tr>
                <th style="width: 15%; text-align: center;">EMPLOYEE<br>ID</th>
                <th style="width: 40%; text-align: center;">NAME</th>
                <th style="width: 30%; text-align: center;">DESIGNATION</th>
                <th style="width: 15%; text-align: center;">TOTAL SALARY</th>
            </tr>
        </thead>
        <tbody>
            @foreach($uniqueEmployees as $emp)
                @php
                    // Get the employee ID from profile, fallback to user ID if not available
                    $employeeId = $emp->profile->employee_id ?? $emp->id ?? 'N/A';
                    $employeeName = $emp->full_name;
                    $designation = $emp->designation->name ?? ($emp->department->name ?? 'N/A');

                    // Calculate total salary from payslips for this employee
                    $employeePayslips = $payslips->where('user_id', $emp->id);
                    $salary = 0;
                    foreach($employeePayslips as $payslip) {
                        $grossPay = $payslip->beneficiaries->where('type', 'allowance')->sum('amount');
                        $salary += $grossPay;
                    }

                    $totalSalary += $salary;
                @endphp
                <tr>
                    <td style="text-align: center;">{{ $employeeId }}</td>
                    <td>{{ $employeeName }}</td>
                    <td>{{ $designation }}</td>
                    <td style="text-align: right;">{{ number_format($salary, 2) }}</td>
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr style="background-color: #e9ecef; font-weight: bold;">
                <td colspan="3" style="text-align: center;">TOTAL</td>
                <td style="text-align: right;">{{ number_format($totalSalary, 2) }}</td>
            </tr>
        </tfoot>
    </table>

    <div class="summary-section">
        <h5>Summary</h5>
        <p><strong>Total Employees:</strong> {{ $uniqueEmployees->count() }}</p>
        <p><strong>Total Salary:</strong> {{ number_format($totalSalary, 2) }}</p>
        <p><strong>Payroll Period:</strong> {{ $payrollPeriod ?? 'MAY 26-JUNE 10, 2024' }}</p>
    </div>

    <div style="margin-top: 50px;">
        <p><strong>Prepared By:</strong></p>
        <br><br>
        <p><strong>Via Mae N. Tembrevilla</strong></p>
        <p>Payroll Analyst</p>
    </div>
</div>
</body>
</html>
