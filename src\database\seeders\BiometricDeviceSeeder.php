<?php

namespace Database\Seeders;

use App\Models\Tenant\BiometricDevice;
use Illuminate\Database\Seeder;

class BiometricDeviceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $devices = [
            [
                'device_id' => 'ZK001',
                'device_name' => 'Main Entrance',
                'mode' => 'automatic',
                'location' => 'Building A - Main Entrance',
                'description' => 'Primary biometric device at main entrance for all employees',
                'is_active' => true,
                'tenant_id' => 1
            ],
            [
                'device_id' => 'ZK002',
                'device_name' => 'Office Floor 1',
                'mode' => 'punch_in_only',
                'location' => 'Building A - Floor 1',
                'description' => 'Punch in device for employees working on floor 1',
                'is_active' => true,
                'tenant_id' => 1
            ],
            [
                'device_id' => 'ZK003',
                'device_name' => 'Office Floor 2',
                'mode' => 'punch_out_only',
                'location' => 'Building A - Floor 2',
                'description' => 'Punch out device for employees working on floor 2',
                'is_active' => true,
                'tenant_id' => 1
            ],
            [
                'device_id' => 'ZK004',
                'device_name' => 'Warehouse',
                'mode' => 'automatic',
                'location' => 'Warehouse Building',
                'description' => 'Biometric device for warehouse staff',
                'is_active' => false,
                'tenant_id' => 1
            ]
        ];

        foreach ($devices as $device) {
            BiometricDevice::updateOrCreate(
                ['device_id' => $device['device_id']],
                $device
            );
        }
    }
}
