<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>All Employees Summary PDF</title>
    <link rel="stylesheet" href="{{ url('css/payslip.css') }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
        }
        .summary-header {
            text-align: center;
            margin-bottom: 30px;
            font-size: 10px;
        }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 10px;
        }
        .summary-table th,
        .summary-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }
        .summary-table th {
            font-weight: bold;
            text-align: center;
            font-size: 10px;
        }
        .payroll-info {
            margin-bottom: 20px;
            font-size: 10px;
        }
        .summary-section {
            margin-top: 30px;
            padding: 15px;
            border: 1px solid #000;
            font-size: 10px;
        }
        .stats-grid {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            font-size: 10px;
        }
        .stats-card {
            width: 23%;
            text-align: center;
            padding: 15px;
            border: 1px solid #000;
            border-radius: 5px;
            font-size: 10px;
        }
        .stats-number {
            font-size: 12px;
            font-weight: bold;
            color: #007bff;
        }
        .stats-label {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: Arial, sans-serif;
            font-size: 10px;
        }
        p {
            font-size: 10px;
        }
    </style>
</head>
<body>
<div>
    <div class="summary-header">
        <img src="{{ property_exists($settings, 'tenant_logo') ? asset($settings->tenant_logo) : asset('images/logo/default-logo.png') }}"
             alt="logo"
             class="img-fluid mb-2"
             style="max-height: 70px; max-width: 70px;"
        />
        <h3 class="font-weight-bold" style="font-size: 13px;">{{ $settings->tenant_name ?? 'Company Name' }}</h3>
        @php
            $addressParts = [];
            if (property_exists($settings, 'address') && !empty($settings->address) && $settings->address !== 'null') {
                $addressParts[] = $settings->address;
            }
            if (property_exists($settings, 'area') && !empty($settings->area) && $settings->area !== 'null') {
                $addressParts[] = $settings->area;
            }
            if (property_exists($settings, 'city') && !empty($settings->city) && $settings->city !== 'null') {
                $addressParts[] = $settings->city;
            }
        @endphp

        @if(!empty($addressParts))
            <p class="mb-2">
                {{ implode(', ', $addressParts) }}
            </p>
        @endif
        <h4 style="font-size: 13px;">ALL EMPLOYEES PAYROLL SUMMARY</h4>
        @php
            $timezone = auth()->user()->timezone ?? settings('time_zone', 'UTC');
            $currentDateTime = now()->setTimezone($timezone);
        @endphp
        <p>Generated on: {{ $currentDateTime->format('F d, Y') }} at {{ $currentDateTime->format('h:i:s A') }}</p>
    </div>

    @php
        // Get unique employees from payslips
        $uniqueEmployees = collect();
        $totalSalary = 0;
        $totalBasic = 0;
        $totalAllowances = 0;
        $totalOvertime = 0;
        $totalGross = 0;
        $totalTax = 0;
        $totalOtherDeductions = 0;
        $totalNetSalary = 0;

        // Get payroll period from payslips or ranges
        $payrollPeriodText = 'N/A';
        if ($payslips->isNotEmpty()) {
            $startDate = $payslips->min('start_date');
            $endDate = $payslips->max('end_date');

            if ($startDate && $endDate) {
                $startFormatted = \Carbon\Carbon::parse($startDate)->format('M d');
                $endFormatted = \Carbon\Carbon::parse($endDate)->format('M d, Y');
                $payrollPeriodText = strtoupper($startFormatted . '-' . $endFormatted);
            }
        } elseif (isset($ranges) && count($ranges) >= 2) {
            $startFormatted = \Carbon\Carbon::parse($ranges[0])->format('M d');
            $endFormatted = \Carbon\Carbon::parse($ranges[1])->format('M d, Y');
            $payrollPeriodText = strtoupper($startFormatted . '-' . $endFormatted);
        }

        foreach($payslips as $payslip) {
            if (!$uniqueEmployees->contains('id', $payslip->user->id)) {
                // Load profile and department relationships if not already loaded
                $payslip->user->load(['profile', 'department', 'designation']);
                $uniqueEmployees->push($payslip->user);
            }

            // Calculate totals
            $totalBasic += $payslip->basic_salary ?? 0;
            $allowanceAmount = $payslip->beneficiaries->where('beneficiary.type', 'allowance')->sum('amount') ?? 0;
            $deductionAmount = $payslip->beneficiaries->where('beneficiary.type', 'deduction')->sum('amount') ?? 0;
            $totalAllowances += $allowanceAmount;
            $totalOvertime += 0; // Overtime is not stored separately in this system
            $totalGross += ($payslip->basic_salary ?? 0) + $allowanceAmount;
            $totalTax += 0; // Tax is not stored separately in this system
            $totalOtherDeductions += $deductionAmount;
            $totalNetSalary += $payslip->net_salary ?? 0;
        }

        $totalSalary = $totalNetSalary;
    @endphp

    <table class="summary-table">
        <thead>
            <tr>
                <th colspan="7" style="text-align: center; padding: 10px; font-size: 13px; font-weight: bold;">PAYROLL COVERED AS OF {{ $payrollPeriodText }}</th>
            </tr>
            <tr>
                <th style="width: 12%; text-align: center;">EMPLOYEE<br>ID</th>
                <th style="width: 25%; text-align: center;">NAME</th>
                <th style="width: 20%; text-align: center;">DESIGNATION</th>
                <th style="width: 12%; text-align: center;">BASIC<br>SALARY</th>
                <th style="width: 10%; text-align: center;">ALLOWANCES</th>
                <th style="width: 10%; text-align: center;">GROSS<br>PAY</th>
                <th style="width: 11%; text-align: center;">NET<br>SALARY</th>
            </tr>
        </thead>
        <tbody>
            @foreach($uniqueEmployees as $emp)
                @php
                    // Get the employee ID from profile, fallback to user ID if not available
                    $employeeId = $emp->profile->employee_id ?? $emp->id ?? 'N/A';
                    $employeeName = $emp->full_name;
                    $designation = $emp->designation->name ?? ($emp->department->name ?? 'N/A');

                    // Calculate totals for this employee from their payslips
                    $employeePayslips = $payslips->where('user_id', $emp->id);
                    $empBasic = $employeePayslips->sum('basic_salary');
                    $empAllowances = 0;
                    $empGross = 0;
                    $empNet = $employeePayslips->sum('net_salary');

                    foreach($employeePayslips as $payslip) {
                        $allowanceAmount = $payslip->beneficiaries->where('beneficiary.type', 'allowance')->sum('amount') ?? 0;
                        $empAllowances += $allowanceAmount;
                        $empGross += ($payslip->basic_salary ?? 0) + $allowanceAmount;
                    }
                @endphp
                <tr>
                    <td style="text-align: center;">{{ $employeeId }}</td>
                    <td>{{ $employeeName }}</td>
                    <td>{{ $designation }}</td>
                    <td style="text-align: right;" class="currency-symbol">
                        {{ trans('default.like_'.$settings->currency_position, [
                            'symbol' => $settings->currency_symbol,
                            'amount' => number_format($empBasic, 2)
                        ]) }}
                    </td>
                    <td style="text-align: right;" class="currency-symbol">
                        {{ trans('default.like_'.$settings->currency_position, [
                            'symbol' => $settings->currency_symbol,
                            'amount' => number_format($empAllowances, 2)
                        ]) }}
                    </td>
                    <td style="text-align: right;" class="currency-symbol">
                        {{ trans('default.like_'.$settings->currency_position, [
                            'symbol' => $settings->currency_symbol,
                            'amount' => number_format($empGross, 2)
                        ]) }}
                    </td>
                    <td style="text-align: right;" class="currency-symbol">
                        {{ trans('default.like_'.$settings->currency_position, [
                            'symbol' => $settings->currency_symbol,
                            'amount' => number_format($empNet, 2)
                        ]) }}
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="summary-section">
        <h5 style="font-size: 13px;">Detailed Summary</h5>
        <div style="display: flex; justify-content: space-between;">
            <div style="width: 48%;">
                <p><strong>Total Employees:</strong> {{ $uniqueEmployees->count() }}</p>
                <p><strong>Total Payslips:</strong> {{ $payslips->count() }}</p>
                <p><strong>Total Basic Salary:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalBasic, 2)]) }}</span></p>
                <p><strong>Total Allowances:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalAllowances, 2)]) }}</span></p>
                <p><strong>Total Overtime:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalOvertime, 2)]) }}</span></p>
            </div>
            <div style="width: 48%;">
                <p><strong>Total Gross Pay:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalGross, 2)]) }}</span></p>
                <p><strong>Total Tax:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalTax, 2)]) }}</span></p>
                <p><strong>Total Other Deductions:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalOtherDeductions, 2)]) }}</span></p>
                <p><strong>Total Net Salary:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalNetSalary, 2)]) }}</span></p>
                <p><strong>Payroll Period:</strong> {{ $payrollPeriodText }}</p>
            </div>
        </div>
    </div>

    <div style="margin-top: 20px; text-align: center;">
        <p>Prepared by:</p>
       <p><strong>{{ auth()->user()->full_name ?? 'System Administrator' }}</strong></p>
        <p>Payroll Analyst</p>
    </div>
</div>
</body>
</html>
