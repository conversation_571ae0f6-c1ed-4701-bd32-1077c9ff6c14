<template>
  <div>
    <!-- Overlay Loader -->
    <app-overlay-loader v-if="preloader" />

    <!-- Error Message -->
    <div v-if="!preloader && error" class="alert alert-danger">
      <strong>Error:</strong> {{ error }}
    </div>

    <!-- Subscription Details -->
    <div v-else-if="subscription">
      <SubscriptionAlert :days-left="daysLeft" in-subscription />

      <!-- Employee Limit Warning -->
      <div v-if="tenantMembers > subscription.plan.employee_limit">
        <div class="alert alert-warning">
          <div class="d-flex align-items-center mb-2">
            <i class="fas fa-exclamation-triangle text-warning mr-2"></i>
            <strong class="h6 mb-0">Employee Limit Exceeded!</strong>
          </div>
          You have exceeded the employee limit for your current subscription plan.
          <strong>Current employees: {{ tenantMembers }}</strong> /
          <strong>Limit: {{ subscription.plan.employee_limit }}</strong>
        </div>
      </div>

      <!-- Current Subscription Header -->
      <div class="mb-4 d-flex justify-content-between">
        <div>
          <h6 class="mb-3 text-secondary">Current Subscription</h6>
          <div class="d-flex justify-content-between">
            <div>
              <div class="d-flex align-items-center gap-2 mb-2">
                <h3 class="mb-0 font-weight-bold mr-2">
                  {{ subscription.plan.plan_name }}
                </h3>
                <span
                  :class="'badge rounded-pill badge-' + getStatusBadgeClass(subscription.status)"
                  style="font-size: 0.875rem; padding: 0.4em 0.75em"
                >
                  {{ formatStatus(subscription.status) }}
                </span>
              </div>
              <p class="mb-0">{{ subscription.plan.plan_description }}</p>
            </div>
          </div>
        </div>
        <div>
          <button class="btn btn-primary" @click="openPlanModal">
            See Plans <i class="fas fa-tags ml-2"></i>
          </button>
        </div>
      </div>

      <!-- Subscription Info -->
      <div class="row mb-4">
        <div class="col-md-6">
          <div class="mb-3">
            <p class="mb-2 text-secondary">Plan Price</p>
            <h2 class="display-5">₱{{ subscription.plan.price }}</h2>
          </div>

          <div class="mb-3">
            <p class="mb-2 text-secondary">Employee Limit</p>
            <h2 class="display-5">{{ subscription.plan.employee_limit }} Employees</h2>
          </div>

          <div class="mb-3">
            <p class="mb-2 text-secondary">Current Employees</p>
            <h2
              class="display-5"
              :class="tenantMembers > subscription.plan.employee_limit ? 'text-danger' : 'text-success'"
            >
              {{ tenantMembers }} / {{ subscription.plan.employee_limit }}
            </h2>

            <div class="progress" style="height: 8px;">
              <div
                class="progress-bar"
                :class="tenantMembers > subscription.plan.employee_limit ? 'bg-danger' : 'bg-success'"
                role="progressbar"
                :style="{ width: getEmployeeUsagePercentage() + '%' }"
                :aria-valuenow="tenantMembers"
                aria-valuemin="0"
                :aria-valuemax="subscription.plan.employee_limit"
              ></div>
            </div>

            <small class="text-muted">
              {{ getEmployeeUsagePercentage() }}% of your employee limit used
            </small>

            <div class="mt-3" v-if="tenantMembers >= subscription.plan.employee_limit">
              <button
                class="btn btn-danger"
                @click="openPlanModal"
                :disabled="canceling"
              >
                <span v-if="canceling">
                  <i class="fas fa-spinner fa-spin"></i> {{ $t('processing') }}
                </span>
                <span v-else>
                  <i class="fa-solid fa-wand-magic-sparkles mr-2"></i> Upgrade Plan
                </span>
              </button>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <p class="mb-2 text-secondary">Features</p>
          <ul class="list-unstyled mb-2">
            <li class="mb-2" v-for="(feature, index) in subscription.plan.features" :key="index">
              <i class="fas fa-check-circle text-success mr-2"></i> {{ feature }}
            </li>
          </ul>
        </div>
      </div>

      <div class="mb-4 d-flex flex-wrap align-items-center gap-3 text-secondary">
        <p class="mb-0 mr-2"><strong>Start Date:</strong> {{ formatDate(subscription.start_date) }}</p>
        <span class="mr-2">|</span>
        <p class="mb-0 mr-2"><strong>End Date:</strong> {{ formatDate(subscription.end_date) }}</p>
        <span class="mr-2">|</span>
        <p class="mb-0 mr-2"><strong>Renewal Date:</strong> {{ formatDate(subscription.renewal_date) }}</p>
        <span class="mr-2">|</span>
        <p class="mb-0 mr-2"><strong>Days Remaining:</strong> {{ calculateRemainingDays(subscription.start_date, subscription.end_date) }} days</p>
      </div>
    </div>

    <!-- No Active Subscription -->
    <div v-else-if="!preloader && !subscription" class="alert alert-warning">
      <strong>No active subscription found.</strong><br />
      Please select a plan to activate your subscription.
      <div class="mt-3">
        <button class="btn btn-primary" @click="openPlanModal">
          See Available Plans <i class="fas fa-tags ml-2"></i>
        </button>
      </div>
    </div>

    <!-- Always Available Plan Modal -->
    <plan-modal
      ref="planModal"
      :current-plan-id="subscription?.plan?.id"
      @plan-selected="handlePlanSelected"
    />
  </div>
</template>

<script>
import { ACTIVE_SUBSCRIPTION } from "../../../../Config/ApiUrl";
import { axiosGet } from "../../../../../common/Helper/AxiosHelper";
import PlanModal from "./PlanModal";
import SubscriptionAlert from "../../../../../common/Components/Helper/SubscriptionAlert";

export default {
  name: "SubscriptionSettings",
  components: {
    PlanModal,
    SubscriptionAlert
  },
  data() {
    return {
      preloader: true,
      canceling: false,
      tenantMembers: 0,
      subscription: null,
      error: null,
      daysLeft: 0,
      renewalChecker: false
    };
  },
  methods: {
    calculateRemainingDays(startDate, endDate) {
      const today = new Date();
      const end = new Date(endDate);
      if (today > end) return 0;
      const diff = end - today;
      return Math.ceil(diff / (1000 * 60 * 60 * 24));
    },
    fetchSubscription() {
      this.preloader = true;
      this.error = null;

      axiosGet(ACTIVE_SUBSCRIPTION)
        .then(({ data }) => {
          if (!data.data) {
            this.subscription = null;
            this.tenantMembers = 0;
            this.daysLeft = 0;
            this.renewalChecker = false;
            return;
          }

          if (typeof data.data.plan.features === 'string') {
            data.data.plan.features = JSON.parse(data.data.plan.features);
          }

          this.subscription = data.data;
          this.tenantMembers = data.tenant_members;
          this.renewalChecker = data.renewal_checker;
          this.daysLeft = data.days_left;
        })
        .catch(error => {
          this.subscription = null;
          this.error = error.response?.data?.message || 'Failed to fetch subscription';
          this.$toastr.e('', this.error);
        })
        .finally(() => {
          this.preloader = false;
        });
    },
    formatDate(date) {
      if (!date) return 'N/A';
      return new Date(date).toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },
    formatStatus(status) {
      return {
        0: this.$t('inactive'),
        1: this.$t('active'),
        2: this.$t('canceled')
      }[status] || this.$t('unknown');
    },
    getStatusBadgeClass(status) {
      return {
        0: 'secondary',
        1: 'success',
        2: 'danger'
      }[status] || 'dark';
    },
    getEmployeeUsagePercentage() {
      if (!this.subscription || !this.tenantMembers) return 0;
      return Math.round((this.tenantMembers / this.subscription.plan.employee_limit) * 100);
    },
    openPlanModal() {
      this.$refs.planModal.showModal();
    },
    handlePlanSelected(plan) {
      this.$toastr.s('', `Selected plan: ${plan.plan_name}`);
      this.fetchSubscription();
    }
  },
  created() {
    this.fetchSubscription();
  }
};
</script>
