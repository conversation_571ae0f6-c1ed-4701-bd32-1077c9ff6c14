<?php

namespace Tests\Feature;

use App\Models\Core\Auth\User;
use App\Models\Tenant\Employee\Profile;
use App\Models\Tenant\Attendance\Attendance;
use App\Models\Tenant\Attendance\AttendanceDetails;
use App\Repositories\Core\Status\StatusRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class BiometricControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user with profile
        $this->user = User::factory()->create();
        $this->user->profile()->create([
            'employee_id' => 'EMP-1', // Using EMP- prefix format
            'user_id' => $this->user->id
        ]);
    }

    /** @test */
    public function it_can_process_biometric_punch_in_data()
    {
        // Simulate ZKTeco ATTLOG data for punch in (numeric ID will be mapped to EMP-1)
        $attlogData = "ATTLOG\t1\t2024-01-15 09:00:00\t0\t1";
        
        $response = $this->post('/iclock/cdata', [], [
            'CONTENT_TYPE' => 'application/x-www-form-urlencoded'
        ]);
        
        // Mock the raw input
        $this->app->instance('request', request()->merge([
            'php://input' => $attlogData
        ]));
        
        $response = $this->call('POST', '/iclock/cdata', [], [], [], [
            'CONTENT_TYPE' => 'application/x-www-form-urlencoded'
        ], $attlogData);
        
        $response->assertStatus(200);
        $response->assertSeeText('OK');
        
        // Verify attendance record was created
        $this->assertDatabaseHas('attendances', [
            'user_id' => $this->user->id,
            'in_date' => '2024-01-15'
        ]);
        
        // Verify attendance details record was created
        $this->assertDatabaseHas('attendance_details', [
            'in_time' => '2024-01-15 09:00:00'
        ]);
    }

    /** @test */
    public function it_can_process_biometric_punch_out_data()
    {
        // First create a punch in record
        $attendance = Attendance::create([
            'user_id' => $this->user->id,
            'in_date' => '2024-01-15',
            'status_id' => resolve(StatusRepository::class)->attendanceApprove(),
            'working_shift_id' => 1,
            'behavior' => 'regular'
        ]);
        
        AttendanceDetails::create([
            'attendance_id' => $attendance->id,
            'in_time' => '2024-01-15 09:00:00',
            'status_id' => resolve(StatusRepository::class)->attendanceApprove()
        ]);
        
        // Simulate ZKTeco ATTLOG data for punch out (numeric ID will be mapped to EMP-1)
        $attlogData = "ATTLOG\t1\t2024-01-15 17:00:00\t1\t1";
        
        $response = $this->call('POST', '/iclock/cdata', [], [], [], [
            'CONTENT_TYPE' => 'application/x-www-form-urlencoded'
        ], $attlogData);
        
        $response->assertStatus(200);
        
        // Verify attendance details record was updated with out_time
        $this->assertDatabaseHas('attendance_details', [
            'attendance_id' => $attendance->id,
            'out_time' => '2024-01-15 17:00:00'
        ]);
    }

    /** @test */
    public function it_handles_unknown_employee_id_gracefully()
    {
        Log::shouldReceive('info')->once();
        Log::shouldReceive('warning')->once()->with('User with biometric ID UNKNOWN not found (tried both raw ID and EMP-UNKNOWN)');

        // Simulate ZKTeco ATTLOG data with unknown employee ID
        $attlogData = "ATTLOG\tUNKNOWN\t2024-01-15 09:00:00\t0\t1";

        $response = $this->call('POST', '/iclock/cdata', [], [], [], [
            'CONTENT_TYPE' => 'application/x-www-form-urlencoded'
        ], $attlogData);

        $response->assertStatus(200);

        // Verify no attendance record was created
        $this->assertDatabaseMissing('attendances', [
            'in_date' => '2024-01-15'
        ]);
    }

    /** @test */
    public function it_maps_numeric_biometric_id_to_emp_prefix()
    {
        // Create another user with numeric employee ID (without EMP- prefix)
        $userWithNumericId = User::factory()->create();
        $userWithNumericId->profile()->create([
            'employee_id' => '2', // Raw numeric ID
            'user_id' => $userWithNumericId->id
        ]);

        // Simulate ZKTeco ATTLOG data with numeric ID
        $attlogData = "ATTLOG\t2\t2024-01-15 09:00:00\t0\t1";

        $response = $this->call('POST', '/iclock/cdata', [], [], [], [
            'CONTENT_TYPE' => 'application/x-www-form-urlencoded'
        ], $attlogData);

        $response->assertStatus(200);

        // Verify attendance record was created for the user with numeric ID
        $this->assertDatabaseHas('attendances', [
            'user_id' => $userWithNumericId->id,
            'in_date' => '2024-01-15'
        ]);
    }
}
