<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Consolidation PDF</title>
    <link rel="stylesheet" href="{{ url('css/payslip.css') }}">
    <style>
        .consolidation-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .consolidation-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 12px;
        }
        .consolidation-table th,
        .consolidation-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        .consolidation-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .employee-info {
            margin-bottom: 20px;
        }
        .breakdown-section {
            margin-top: 30px;
        }
        .breakdown-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 11px;
        }
        .breakdown-table th,
        .breakdown-table td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: center;
        }
        .breakdown-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .summary-totals {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 2px solid #007bff;
        }
    </style>
</head>
<body>
<div>
    <div class="consolidation-header">
        <img src="{{ property_exists($settings, 'tenant_logo') ? asset($settings->tenant_logo) : asset('images/logo/default-logo.png') }}"
             alt="logo"
             class="img-fluid mb-2"
             style="max-height: 100px; max-width: 150px;"
        />
        <h3 class="font-weight-bold">{{ $settings->tenant_name ?? 'Company Name' }}</h3>
        @php
            $addressParts = [];
            if (property_exists($settings, 'address') && !empty($settings->address) && $settings->address !== 'null') {
                $addressParts[] = $settings->address;
            }
            if (property_exists($settings, 'area') && !empty($settings->area) && $settings->area !== 'null') {
                $addressParts[] = $settings->area;
            }
            if (property_exists($settings, 'city') && !empty($settings->city) && $settings->city !== 'null') {
                $addressParts[] = $settings->city;
            }
            if (property_exists($settings, 'zip_code') && !empty($settings->zip_code) && $settings->zip_code !== 'null') {
                $addressParts[] = 'ZIP-code:' . $settings->zip_code;
            }
            if (property_exists($settings, 'country') && !empty($settings->country) && $settings->country !== 'null') {
                $addressParts[] = $settings->country;
            }
        @endphp

        @if(!empty($addressParts))
            <p class="mb-2">
                {{ implode(', ', $addressParts) }}
            </p>
        @endif
        <h4>PAYROLL CONSOLIDATION REPORT</h4>
        @php
            $timezone = auth()->user()->timezone ?? settings('time_zone', 'UTC');
            $currentDateTime = now()->setTimezone($timezone);
        @endphp
        <p>Generated on: {{ $currentDateTime->format('F d, Y') }} at {{ $currentDateTime->format('h:i:s A') }}</p>
    </div>

    <div class="employee-info">
        <h5>Employee Information</h5>
        <p><strong>Name:</strong> {{ $employee->full_name }}</p>
        <p><strong>Email:</strong> {{ $employee->email }}</p>
        <p><strong>Department:</strong> {{ $employee->department->name ?? 'N/A' }}</p>
        <p><strong>Employee ID:</strong> {{ $employee->profile->employee_id ?? 'N/A' }}</p>
        <p><strong>Report Period:</strong> {{ $payslips->min('start_date') ? \Carbon\Carbon::parse($payslips->min('start_date'))->format('M d, Y') : 'N/A' }} - {{ $payslips->max('end_date') ? \Carbon\Carbon::parse($payslips->max('end_date'))->format('M d, Y') : 'N/A' }}</p>
    </div>

    <table class="consolidation-table">
        <thead>
            <tr>
                <th>Pay Period</th>
                <th>Payrun ID</th>
                <th>Basic Salary</th>
                <th>Allowances</th>
                <th>Overtime</th>
                <th>Gross Pay</th>
                <th>Tax</th>
                <th>Other Deductions</th>
                <th>Total Deductions</th>
                <th>Net Pay</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalBasic = 0;
                $totalAllowances = 0;
                $totalOvertime = 0;
                $totalGross = 0;
                $totalTax = 0;
                $totalOtherDeductions = 0;
                $totalDeductions = 0;
                $totalNet = 0;
            @endphp
            @foreach($payslips as $payslip)
                @php
                    $basicSalary = $payslip->beneficiaries->where('type', 'allowance')->where('name', 'like', '%basic%')->sum('amount');
                    $allowances = $payslip->beneficiaries->where('type', 'allowance')->where('name', 'not like', '%basic%')->where('name', 'not like', '%overtime%')->sum('amount');
                    $overtime = $payslip->beneficiaries->where('type', 'allowance')->where('name', 'like', '%overtime%')->sum('amount');
                    $grossPay = $payslip->beneficiaries->where('type', 'allowance')->sum('amount');
                    $tax = $payslip->beneficiaries->where('type', 'deduction')->where('name', 'like', '%tax%')->sum('amount');
                    $otherDeductions = $payslip->beneficiaries->where('type', 'deduction')->where('name', 'not like', '%tax%')->sum('amount');
                    $deductions = $payslip->beneficiaries->where('type', 'deduction')->sum('amount');
                    $netPay = $grossPay - $deductions;

                    $totalBasic += $basicSalary;
                    $totalAllowances += $allowances;
                    $totalOvertime += $overtime;
                    $totalGross += $grossPay;
                    $totalTax += $tax;
                    $totalOtherDeductions += $otherDeductions;
                    $totalDeductions += $deductions;
                    $totalNet += $netPay;
                @endphp
                <tr>
                    <td>{{ \Carbon\Carbon::parse($payslip->start_date)->format('M d') }} - {{ \Carbon\Carbon::parse($payslip->end_date)->format('M d, Y') }}</td>
                    <td>{{ $payslip->payrun->uid }}</td>
                    <td>${{ number_format($basicSalary, 2) }}</td>
                    <td>${{ number_format($allowances, 2) }}</td>
                    <td>${{ number_format($overtime, 2) }}</td>
                    <td>${{ number_format($grossPay, 2) }}</td>
                    <td>${{ number_format($tax, 2) }}</td>
                    <td>${{ number_format($otherDeductions, 2) }}</td>
                    <td>${{ number_format($deductions, 2) }}</td>
                    <td>${{ number_format($netPay, 2) }}</td>
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr style="background-color: #007bff; color: white; font-weight: bold;">
                <td colspan="2">GRAND TOTAL</td>
                <td>${{ number_format($totalBasic, 2) }}</td>
                <td>${{ number_format($totalAllowances, 2) }}</td>
                <td>${{ number_format($totalOvertime, 2) }}</td>
                <td>${{ number_format($totalGross, 2) }}</td>
                <td>${{ number_format($totalTax, 2) }}</td>
                <td>${{ number_format($totalOtherDeductions, 2) }}</td>
                <td>${{ number_format($totalDeductions, 2) }}</td>
                <td>${{ number_format($totalNet, 2) }}</td>
            </tr>
        </tfoot>
    </table>

    <div class="summary-totals">
        <h5>Consolidation Summary</h5>
        <div style="display: flex; justify-content: space-between;">
            <div style="width: 48%;">
                <p><strong>Total Pay Periods:</strong> {{ $payslips->count() }}</p>
                <p><strong>Total Basic Salary:</strong> ${{ number_format($totalBasic, 2) }}</p>
                <p><strong>Total Allowances:</strong> ${{ number_format($totalAllowances, 2) }}</p>
                <p><strong>Total Overtime:</strong> ${{ number_format($totalOvertime, 2) }}</p>
                <p><strong>Total Gross Pay:</strong> ${{ number_format($totalGross, 2) }}</p>
            </div>
            <div style="width: 48%;">
                <p><strong>Total Tax:</strong> ${{ number_format($totalTax, 2) }}</p>
                <p><strong>Total Other Deductions:</strong> ${{ number_format($totalOtherDeductions, 2) }}</p>
                <p><strong>Total Deductions:</strong> ${{ number_format($totalDeductions, 2) }}</p>
                <p><strong>Total Net Pay:</strong> ${{ number_format($totalNet, 2) }}</p>
                <p><strong>Average Net Pay:</strong> ${{ $payslips->count() > 0 ? number_format($totalNet / $payslips->count(), 2) : '0.00' }}</p>
            </div>
        </div>
    </div>

    <div style="margin-top: 30px;">
        <p><strong>Prepared by:</strong> {{ auth()->user()->full_name }}</p>
    </div>
</div>
</body>
</html>
