<?php

namespace Database\Seeders;

use App\Models\Tenant\Subscription as TenantSubscription;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DefaultSubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenantId = env('DEFAULT_TENANT_ID');
        $planId = env('DEFAULT_PLAN_SUBSCRIPTION');

        if (!$tenantId || !$planId) {
            $this->command->error('DEFAULT_TENANT_ID or DEFAULT_PLAN_SUBSCRIPTION is not set in .env');
            return;
        }

        DB::beginTransaction();

        try {
            TenantSubscription::create([
                'tenant_id' => $tenantId,
                'plan_id' => $planId,
                'start_date' => Carbon::now(),
                'end_date' => Carbon::now()->addMonth(),
                'renewal_date' => Carbon::now()->addYear()->subDays(5),
                'status' => 1, // Active
            ]);

            DB::commit();

            $this->command->info('Default subscription created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Failed to create default subscription: ' . $e->getMessage());
        }
    }
}
