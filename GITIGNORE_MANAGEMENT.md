# Git Ignore Management System

This project uses an intelligent `.gitignore` management system that automatically handles build artifacts based on whether you're running development or production builds.

## How It Works

### Development Mode
When running development commands (`npm run dev`, `npm run watch`, `npm run hot`):
- Build artifacts are **ignored** by git
- Source maps are ignored
- Hot reload files are ignored
- This keeps your repository clean during development

### Production Mode
When running production commands (`npm run prod`, `npm run build`):
- Build artifacts are **tracked** by git
- Only development-specific files are ignored
- This ensures production builds are committed to the repository

## Available Commands

```bash
# Development builds (ignores build artifacts)
npm run dev          # Single build
npm run watch        # Watch for changes
npm run watch-poll   # Watch with polling
npm run hot          # Hot module replacement

# Production builds (tracks build artifacts)
npm run prod         # Production build
npm run build        # Alias for prod

# Manual gitignore management
npm run gitignore:dev      # Configure for development
npm run gitignore:prod     # Configure for production
npm run gitignore:restore  # Restore original .gitignore
```

## Files Involved

- `.gitignore` - Main gitignore file (automatically managed)
- `.gitignore-dev` - Development-specific ignore rules
- `.gitignore.backup` - Backup of original .gitignore
- `src/scripts/manage-gitignore.js` - Script that manages gitignore behavior

## What Gets Ignored/Tracked

### Always Ignored
- `/node_modules/`
- `/vendor/`
- Environment files (`.env`)
- IDE files (`.vscode/`, `.idea/`)
- System files (`.DS_Store`, `Thumbs.db`)

### Development Mode (Ignored)
- `/js/` (build output)
- `/css/` (build output)
- `/fonts/` (build output)
- `/mix-manifest.json`
- `*.map` (source maps)
- `/src/public/hot` (hot reload)

### Production Mode (Tracked)
- `/js/` (build output) ✓
- `/css/` (build output) ✓
- `/fonts/` (build output) ✓
- `/mix-manifest.json` ✓

## Automatic Behavior

The system automatically:
1. Configures `.gitignore` when you run npm scripts
2. Updates `.gitignore` after webpack builds complete
3. Backs up your original `.gitignore` before making changes
4. Restores appropriate settings based on build mode

## Manual Override

If you need to manually control the gitignore behavior:

```bash
# Force development mode
npm run gitignore:dev

# Force production mode
npm run gitignore:prod

# Restore original .gitignore
npm run gitignore:restore
```

## Troubleshooting

### If build artifacts are being tracked when they shouldn't be:
```bash
npm run gitignore:dev
git rm -r --cached js/ css/ fonts/ mix-manifest.json
git commit -m "Remove build artifacts from tracking"
```

### If build artifacts aren't being tracked in production:
```bash
npm run gitignore:prod
git add js/ css/ fonts/ mix-manifest.json
git commit -m "Add production build artifacts"
```

### To completely reset the system:
```bash
npm run gitignore:restore
# Then run your desired build command
```

## Benefits

1. **Clean Development**: Build artifacts don't clutter your git history during development
2. **Production Ready**: Production builds are properly tracked for deployment
3. **Automatic**: No manual intervention required - just run your normal npm commands
4. **Flexible**: Can be manually overridden when needed
5. **Safe**: Always backs up your original .gitignore before making changes
