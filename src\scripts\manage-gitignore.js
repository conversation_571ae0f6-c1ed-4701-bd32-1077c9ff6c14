#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const mode = process.argv[2] || 'dev';
const rootDir = path.resolve(__dirname, '../..');
const gitignorePath = path.join(rootDir, '.gitignore');
const gitignoreDevPath = path.join(rootDir, '.gitignore-dev');
const gitignoreBackupPath = path.join(rootDir, '.gitignore.backup');

console.log(`Managing .gitignore for mode: ${mode}`);

function backupGitignore() {
    if (fs.existsSync(gitignorePath) && !fs.existsSync(gitignoreBackupPath)) {
        fs.copyFileSync(gitignorePath, gitignoreBackupPath);
        console.log('✓ Backed up original .gitignore');
    }
}

function restoreGitignore() {
    if (fs.existsSync(gitignoreBackupPath)) {
        fs.copyFileSync(gitignoreBackupPath, gitignorePath);
        console.log('✓ Restored original .gitignore');
    }
}

function setupDevGitignore() {
    backupGitignore();
    
    let gitignoreContent = '';
    
    // Read base .gitignore
    if (fs.existsSync(gitignorePath)) {
        gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    }
    
    // Add development-specific ignores
    if (fs.existsSync(gitignoreDevPath)) {
        const devIgnores = fs.readFileSync(gitignoreDevPath, 'utf8');
        gitignoreContent += '\n\n# === DEVELOPMENT MODE IGNORES ===\n';
        gitignoreContent += '# These are automatically added during development builds\n';
        gitignoreContent += devIgnores;
    }
    
    fs.writeFileSync(gitignorePath, gitignoreContent);
    console.log('✓ Configured .gitignore for development mode');
    console.log('  - Build artifacts will be ignored');
    console.log('  - Source maps will be ignored');
    console.log('  - Hot reload files will be ignored');
}

function setupProdGitignore() {
    restoreGitignore();
    
    let gitignoreContent = '';
    
    // Read base .gitignore
    if (fs.existsSync(gitignorePath)) {
        gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    }
    
    // For production, we want to track build artifacts but ignore dev-specific files
    gitignoreContent += '\n\n# === PRODUCTION MODE ===\n';
    gitignoreContent += '# Build artifacts are tracked in production\n';
    gitignoreContent += '# Only ignore development-specific files\n';
    gitignoreContent += '/src/public/hot\n';
    gitignoreContent += '*.map\n';
    gitignoreContent += '.DS_Store\n';
    gitignoreContent += 'Thumbs.db\n';
    
    fs.writeFileSync(gitignorePath, gitignoreContent);
    console.log('✓ Configured .gitignore for production mode');
    console.log('  - Build artifacts will be tracked');
    console.log('  - Only development files will be ignored');
}

switch (mode) {
    case 'dev':
    case 'development':
        setupDevGitignore();
        break;
        
    case 'prod':
    case 'production':
        setupProdGitignore();
        break;
        
    case 'restore':
        restoreGitignore();
        console.log('✓ Restored original .gitignore');
        break;
        
    default:
        console.error(`Unknown mode: ${mode}`);
        console.error('Usage: node manage-gitignore.js [dev|prod|restore]');
        process.exit(1);
}
