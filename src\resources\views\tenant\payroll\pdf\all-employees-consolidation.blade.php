<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>All Employees Consolidation PDF</title>
    <link rel="stylesheet" href="{{ url('css/payslip.css') }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
        }
        .consolidation-header {
            text-align: center;
            margin-bottom: 30px;
            font-size: 10px;
        }
        .consolidation-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 10px;
        }
        .consolidation-table th,
        .consolidation-table td {
            border: 1px solid #000;
            padding: 4px;
            text-align: left;
            font-size: 10px;
        }
        .consolidation-table th {
            font-weight: bold;
            text-align: center;
            font-size: 10px;
        }
        .employee-info {
            margin-bottom: 20px;
            font-size: 10px;
        }
        .breakdown-section {
            margin-top: 30px;
            font-size: 10px;
        }
        .breakdown-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 10px;
        }
        .breakdown-table th,
        .breakdown-table td {
            border: 1px solid #000;
            padding: 4px;
            text-align: center;
            font-size: 10px;
        }
        .breakdown-table th {
            font-weight: bold;
            font-size: 10px;
        }
        .summary-totals {
            margin-top: 30px;
            padding: 15px;
            border: 2px solid #000;
            font-size: 10px;
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: Arial, sans-serif;
            font-size: 10px;
        }
        p {
            font-size: 10px;
        }
    </style>
</head>
<body>
<div>
    <div class="consolidation-header">
        <img src="{{ property_exists($settings, 'tenant_logo') ? asset($settings->tenant_logo) : asset('images/logo/default-logo.png') }}"
             alt="logo"
             class="img-fluid mb-2"
             style="max-height: 70px; max-width: 70px;"
        />
        <h3 class="font-weight-bold" style="font-size: 13px;">{{ $settings->tenant_name ?? 'Company Name' }}</h3>
        @php
            $addressParts = [];
            if (property_exists($settings, 'address') && !empty($settings->address) && $settings->address !== 'null') {
                $addressParts[] = $settings->address;
            }
            if (property_exists($settings, 'area') && !empty($settings->area) && $settings->area !== 'null') {
                $addressParts[] = $settings->area;
            }
            if (property_exists($settings, 'city') && !empty($settings->city) && $settings->city !== 'null') {
                $addressParts[] = $settings->city;
            }
        @endphp

        @if(!empty($addressParts))
            <p class="mb-2">
                {{ implode(', ', $addressParts) }}
            </p>
        @endif
        <h4 style="font-size: 13px;">ALL EMPLOYEES PAYROLL CONSOLIDATION REPORT</h4>
        @php
            $timezone = auth()->user()->timezone ?? settings('time_zone', 'UTC');
            $currentDateTime = now()->setTimezone($timezone);
        @endphp
        <p>Generated on: {{ $currentDateTime->format('F d, Y') }} at {{ $currentDateTime->format('h:i:s A') }}</p>
    </div>

    @php
        // Get payroll period from payslips or ranges
        $payrollPeriodText = 'N/A';
        $payrollPeriodFormatted = 'N/A';

        if ($payslips->isNotEmpty()) {
            $startDate = $payslips->min('start_date');
            $endDate = $payslips->max('end_date');

            if ($startDate && $endDate) {
                $startFormatted = \Carbon\Carbon::parse($startDate)->format('M d, Y');
                $endFormatted = \Carbon\Carbon::parse($endDate)->format('M d, Y');
                $payrollPeriodText = $startFormatted . ' - ' . $endFormatted;

                // Format for the "COVERED AS OF" section (MAY 01-JUL 31, 2025 format)
                $startFormattedCovered = \Carbon\Carbon::parse($startDate)->format('M d');
                $endFormattedCovered = \Carbon\Carbon::parse($endDate)->format('M d, Y');
                $payrollPeriodFormatted = strtoupper($startFormattedCovered . '-' . $endFormattedCovered);
            }
        } elseif (isset($ranges) && count($ranges) >= 2) {
            $startFormatted = \Carbon\Carbon::parse($ranges[0])->format('M d, Y');
            $endFormatted = \Carbon\Carbon::parse($ranges[1])->format('M d, Y');
            $payrollPeriodText = $startFormatted . ' - ' . $endFormatted;

            // Format for the "COVERED AS OF" section
            $startFormattedCovered = \Carbon\Carbon::parse($ranges[0])->format('M d');
            $endFormattedCovered = \Carbon\Carbon::parse($ranges[1])->format('M d, Y');
            $payrollPeriodFormatted = strtoupper($startFormattedCovered . '-' . $endFormattedCovered);
        }
    @endphp

    @php
        // Group payslips by employee
        $employeePayslips = $payslips->groupBy('user_id');
    @endphp

    <table class="consolidation-table">
        <thead>
            <tr>
                <th colspan="10" style="text-align: center; padding: 10px; font-size: 13px; font-weight: bold;">PAYROLL COVERED AS OF {{ $payrollPeriodText }}</th>
            </tr>
            <tr>
                <th style="width: 8%;">Emp ID</th>
                <th style="width: 20%;">Employee Name</th>
                <th style="width: 15%;">Department</th>
                <th style="width: 10%;">Basic Salary</th>
                <th style="width: 10%;">Allowances</th>
                <th style="width: 8%;">Overtime</th>
                <th style="width: 10%;">Gross Pay</th>
                <th style="width: 8%;">Tax</th>
                <th style="width: 8%;">Other Ded.</th>
                <th style="width: 10%;">Net Pay</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalBasic = 0;
                $totalAllowances = 0;
                $totalOvertime = 0;
                $totalGross = 0;
                $totalTax = 0;
                $totalOtherDeductions = 0;
                $totalDeductions = 0;
                $totalNet = 0;
            @endphp
            @foreach($employeePayslips as $userId => $userPayslips)
                @php
                    $employee = $userPayslips->first()->user;
                    $employee->load(['profile', 'department', 'designation']);
                    $employeeId = $employee->profile->employee_id ?? $employee->id ?? 'N/A';
                    $employeeName = $employee->full_name;
                    $department = $employee->department->name ?? 'N/A';

                    $empBasicSalary = 0;
                    $empAllowances = 0;
                    $empOvertime = 0;
                    $empGrossPay = 0;
                    $empTax = 0;
                    $empOtherDeductions = 0;
                    $empDeductions = 0;
                    $empNetPay = 0;

                    foreach($userPayslips as $payslip) {
                        // Use the actual basic salary from the payslip
                        $basicSalary = $payslip->basic_salary;

                        // Use the already calculated net salary from the system
                        $netPay = $payslip->net_salary;

                        // Calculate total allowances and deductions from beneficiaries
                        $totalAllowances = 0;
                        $totalDeductions = 0;

                        foreach($payslip->beneficiaries as $beneficiary) {
                            $amount = $beneficiary->is_percentage ?
                                ($basicSalary / 100) * $beneficiary->amount :
                                $beneficiary->amount;

                            if ($beneficiary->beneficiary->type == 'allowance') {
                                $totalAllowances += $amount;
                            } else {
                                $totalDeductions += $amount;
                            }
                        }

                        // Calculate gross pay based on the system's logic
                        // The net_salary already includes attendance-based calculations
                        $grossPay = $netPay + $totalDeductions;

                        // For breakdown purposes, try to identify specific allowance types
                        $allowances = 0;
                        $overtime = 0;
                        $tax = 0;
                        $otherDeductions = 0;

                        foreach($payslip->beneficiaries as $beneficiary) {
                            $amount = $beneficiary->is_percentage ?
                                ($basicSalary / 100) * $beneficiary->amount :
                                $beneficiary->amount;

                            if ($beneficiary->beneficiary->type == 'allowance') {
                                if (stripos($beneficiary->beneficiary->name, 'overtime') !== false) {
                                    $overtime += $amount;
                                } else {
                                    $allowances += $amount;
                                }
                            } else {
                                if (stripos($beneficiary->beneficiary->name, 'tax') !== false) {
                                    $tax += $amount;
                                } else {
                                    $otherDeductions += $amount;
                                }
                            }
                        }

                        $empBasicSalary += $basicSalary;
                        $empAllowances += $allowances;
                        $empOvertime += $overtime;
                        $empGrossPay += $grossPay;
                        $empTax += $tax;
                        $empOtherDeductions += $otherDeductions;
                        $empDeductions += $totalDeductions;
                        $empNetPay += $netPay;
                    }

                    $totalBasic += $empBasicSalary;
                    $totalAllowances += $empAllowances;
                    $totalOvertime += $empOvertime;
                    $totalGross += $empGrossPay;
                    $totalTax += $empTax;
                    $totalOtherDeductions += $empOtherDeductions;
                    $totalDeductions += $empDeductions;
                    $totalNet += $empNetPay;
                @endphp
                <tr>
                    <td style="text-align: center;">{{ $employeeId }}</td>
                    <td>{{ $employeeName }}</td>
                    <td>{{ $department }}</td>
                    <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($empBasicSalary, 2)]) }}</td>
                    <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($empAllowances, 2)]) }}</td>
                    <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($empOvertime, 2)]) }}</td>
                    <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($empGrossPay, 2)]) }}</td>
                    <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($empTax, 2)]) }}</td>
                    <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($empOtherDeductions, 2)]) }}</td>
                    <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($empNetPay, 2)]) }}</td>
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr style="font-weight: bold;">
                <td colspan="3" style="text-align: center;">GRAND TOTAL</td>
                <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalBasic, 2)]) }}</td>
                <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalAllowances, 2)]) }}</td>
                <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalOvertime, 2)]) }}</td>
                <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalGross, 2)]) }}</td>
                <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalTax, 2)]) }}</td>
                <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalOtherDeductions, 2)]) }}</td>
                <td style="text-align: right;" class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalNet, 2)]) }}</td>
            </tr>
        </tfoot>
    </table>

    <div class="summary-totals">
        <h5 style="font-size: 13px;">Consolidation Summary</h5>
        <div style="display: flex; justify-content: space-between;">
            <div style="width: 48%;">
                <p><strong>Total Employees:</strong> {{ $employeePayslips->count() }}</p>
                <p><strong>Total Payslips:</strong> {{ $payslips->count() }}</p>
                <p><strong>Total Basic Salary:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalBasic, 2)]) }}</span></p>
                <p><strong>Total Allowances:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalAllowances, 2)]) }}</span></p>
                <p><strong>Total Overtime:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalOvertime, 2)]) }}</span></p>
                <p><strong>Total Gross Pay:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalGross, 2)]) }}</span></p>
            </div>
            <div style="width: 48%;">
                <p><strong>Total Tax:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalTax, 2)]) }}</span></p>
                <p><strong>Total Other Deductions:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalOtherDeductions, 2)]) }}</span></p>
                <p><strong>Total Deductions:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalDeductions, 2)]) }}</span></p>
                <p><strong>Total Net Pay:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => number_format($totalNet, 2)]) }}</span></p>
                <p><strong>Average Net Pay per Employee:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => ($employeePayslips->count() > 0 ? number_format($totalNet / $employeePayslips->count(), 2) : '0.00')]) }}</span></p>
                <p><strong>Average Net Pay per Payslip:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, ['symbol' => $settings->currency_symbol, 'amount' => ($payslips->count() > 0 ? number_format($totalNet / $payslips->count(), 2) : '0.00')]) }}</span></p>
            </div>
        </div>
    </div>

    <div style="margin-top: 20px; text-align: center;">
        <p>Prepared by:</p>
       <p><strong>{{ auth()->user()->full_name ?? 'System Administrator' }}</strong></p>
        <p>Payroll Analyst</p>
    </div>
</div>
</body>
</html>
