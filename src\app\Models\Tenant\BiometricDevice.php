<?php

namespace App\Models\Tenant;

use App\Models\Tenant\TenantModel;

class BiometricDevice extends TenantModel
{
    protected $fillable = [
        'device_id',
        'device_name',
        'mode',
        'location',
        'description',
        'is_active',
        'tenant_id'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public const MODES = [
        'automatic' => 'Automatic (Punch In/Out)',
        'punch_in_only' => 'Punch In Only',
        'punch_out_only' => 'Punch Out Only'
    ];

    /**
     * Get active devices
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get device by device ID
     */
    public function scopeByDeviceId($query, $deviceId)
    {
        return $query->where('device_id', $deviceId);
    }

    /**
     * Check if device supports punch in
     */
    public function supportsPunchIn(): bool
    {
        return in_array($this->mode, ['automatic', 'punch_in_only']);
    }

    /**
     * Check if device supports punch out
     */
    public function supportsPunchOut(): bool
    {
        return in_array($this->mode, ['automatic', 'punch_out_only']);
    }

    /**
     * Get mode display name
     */
    public function getModeDisplayAttribute(): string
    {
        return self::MODES[$this->mode] ?? $this->mode;
    }
}
