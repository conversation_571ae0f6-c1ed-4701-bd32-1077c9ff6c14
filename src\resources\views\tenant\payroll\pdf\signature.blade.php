<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>For Signature PDF</title>
    <link rel="stylesheet" href="{{ url('css/payslip.css') }}">
    <style>
        .signature-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .signature-table th,
        .signature-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            vertical-align: middle;
        }
        .signature-table th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }
        .signature-table td {
            font-size: 11px;
        }
        .payroll-info {
            margin-bottom: 20px;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 45%;
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-top: 50px;
        }
        .approval-section {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
<div>
    <div class="signature-header">
        <img src="{{ property_exists($settings, 'tenant_logo') ? asset($settings->tenant_logo) : asset('images/logo/default-logo.png') }}"
             alt="logo"
             class="img-fluid mb-2"
             style="max-height: 100px; max-width: 150px;"
        />
        <h3 class="font-weight-bold">{{ $settings->tenant_name ?? 'Company Name' }}</h3>
        @php
            $addressParts = [];
            if (property_exists($settings, 'address') && !empty($settings->address) && $settings->address !== 'null') {
                $addressParts[] = $settings->address;
            }
            if (property_exists($settings, 'area') && !empty($settings->area) && $settings->area !== 'null') {
                $addressParts[] = $settings->area;
            }
            if (property_exists($settings, 'city') && !empty($settings->city) && $settings->city !== 'null') {
                $addressParts[] = $settings->city;
            }
            if (property_exists($settings, 'zip_code') && !empty($settings->zip_code) && $settings->zip_code !== 'null') {
                $addressParts[] = 'ZIP-code:' . $settings->zip_code;
            }
            if (property_exists($settings, 'country') && !empty($settings->country) && $settings->country !== 'null') {
                $addressParts[] = $settings->country;
            }
        @endphp

        @if(!empty($addressParts))
            <p class="mb-2">
                {{ implode(', ', $addressParts) }}
            </p>
        @endif
        <h4>PAYROLL FOR SIGNATURE</h4>
        @php
            $timezone = auth()->user()->timezone ?? settings('time_zone', 'UTC');
            $currentDateTime = now()->setTimezone($timezone);
        @endphp
        <p>Generated on: {{ $currentDateTime->format('F d, Y') }} at {{ $currentDateTime->format('h:i:s A') }}</p>
    </div>

    @php
        // Get unique employees from payslips
        $uniqueEmployees = collect();
        $totalSalary = 0;

        // Get payroll period from payslips or ranges
        $payrollPeriodText = 'N/A';
        if ($payslips->isNotEmpty()) {
            $startDate = $payslips->min('start_date');
            $endDate = $payslips->max('end_date');

            if ($startDate && $endDate) {
                $startFormatted = \Carbon\Carbon::parse($startDate)->format('M d');
                $endFormatted = \Carbon\Carbon::parse($endDate)->format('M d, Y');
                $payrollPeriodText = $startFormatted . '-' . $endFormatted;
            }
        }

        foreach($payslips as $payslip) {
            if (!$uniqueEmployees->contains('id', $payslip->user->id)) {
                // Load profile and department relationships if not already loaded
                $payslip->user->load(['profile', 'department', 'designation']);
                $uniqueEmployees->push($payslip->user);
            }
        }
    @endphp

    <div class="payroll-info">
        <h5>ALL EMPLOYEES PAYROLL FOR SIGNATURE AS OF {{ $payrollPeriodText }}</h5>
    </div>

    <table class="signature-table">
        <thead>
            <tr>
                <th style="width: 15%; text-align: center;">EMPLOYEE<br>ID</th>
                <th style="width: 40%; text-align: center;">NAME</th>
                <th style="width: 30%; text-align: center;">DESIGNATION</th>
                <th style="width: 15%; text-align: center;">SIGNATURE</th>
            </tr>
        </thead>
        <tbody>
            @foreach($uniqueEmployees as $emp)
                @php
                    // Get the employee ID from profile, fallback to user ID if not available
                    $employeeId = $emp->profile->employee_id ?? $emp->id ?? 'N/A';
                    $employeeName = $emp->full_name;
                    $designation = $emp->designation->name ?? ($emp->department->name ?? 'N/A');

                    // Calculate total salary from payslips for this employee
                    $employeePayslips = $payslips->where('user_id', $emp->id);
                    $salary = 0;
                    foreach($employeePayslips as $payslip) {
                        // Use the already calculated net salary from the system
                        // For signature, we typically show the total salary (net pay)
                        $salary += $payslip->net_salary;
                    }

                    $totalSalary += $salary;
                @endphp
                <tr>
                    <td style="text-align: center;">{{ $employeeId }}</td>
                    <td>{{ $employeeName }}</td>
                    <td>{{ $designation }}</td>
                    <td style="height: 40px; border-bottom: 1px solid #000; text-align: center;"></td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="approval-section">
        <h5>Summary</h5>
        <p><strong>Total Employees:</strong> {{ $uniqueEmployees->count() }}</p>
        <p><strong>Total Salary:</strong> <span class="currency-symbol">{{ trans('default.like_'.$settings->currency_position, [
            'symbol' => $settings->currency_symbol,
            'amount' => number_format($totalSalary, 2)
        ]) }}</span></p>
        <p><strong>Payroll Period:</strong> {{ $payrollPeriodText }}</p>
        <br>
        <p><strong>I acknowledge receipt of the above payroll amounts and confirm that all information is accurate.</strong></p>
        <br>
        <div style="display: flex; justify-content: space-between; margin-top: 40px;">
            <div style="width: 45%; text-align: center;">
                <div style="border-top: 1px solid #000; padding-top: 10px; margin-top: 50px;">
                    <strong>Employee Representative Signature</strong><br>
                    <small>Date: _______________</small>
                </div>
            </div>
            <div style="width: 45%; text-align: center;">
                <div style="border-top: 1px solid #000; padding-top: 10px; margin-top: 50px;">
                    <strong>HR Manager Signature</strong><br>
                    <small>Date: _______________</small>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top: 30px;">
        <p><strong>Prepared by:</strong> {{ auth()->user()->full_name }}</p>
    </div>
</div>
</body>
</html>
