<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\BiometricDevice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BiometricDeviceController extends Controller
{
    public function index()
    {
        $devices = BiometricDevice::orderBy('created_at', 'desc')->get();
        
        return response()->json([
            'data' => $devices,
            'modes' => BiometricDevice::MODES
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'device_id' => 'required|string|unique:biometric_devices,device_id',
            'device_name' => 'required|string|max:255',
            'mode' => 'required|in:automatic,punch_in_only,punch_out_only',
            'location' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $device = BiometricDevice::create($request->all());

        return response()->json([
            'status' => true,
            'message' => 'Biometric device created successfully',
            'data' => $device
        ]);
    }

    public function show(BiometricDevice $biometricDevice)
    {
        return response()->json([
            'data' => $biometricDevice
        ]);
    }

    public function update(Request $request, BiometricDevice $biometricDevice)
    {
        $validator = Validator::make($request->all(), [
            'device_id' => 'required|string|unique:biometric_devices,device_id,' . $biometricDevice->id,
            'device_name' => 'required|string|max:255',
            'mode' => 'required|in:automatic,punch_in_only,punch_out_only',
            'location' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $biometricDevice->update($request->all());

        return response()->json([
            'status' => true,
            'message' => 'Biometric device updated successfully',
            'data' => $biometricDevice
        ]);
    }

    public function destroy(BiometricDevice $biometricDevice)
    {
        $biometricDevice->delete();

        return response()->json([
            'status' => true,
            'message' => 'Biometric device deleted successfully'
        ]);
    }

    public function toggleStatus(BiometricDevice $biometricDevice)
    {
        $biometricDevice->update([
            'is_active' => !$biometricDevice->is_active
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Device status updated successfully',
            'data' => $biometricDevice
        ]);
    }
}
