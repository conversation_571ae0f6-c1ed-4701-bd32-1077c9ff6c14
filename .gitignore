# Dependencies
/node_modules/
/src/node_modules/
/vendor/
/src/vendor/

# <PERSON>vel specific
/storage/
bootstrap/cache/

# Environment files
/src/.env
/src/public/storage

# Build artifacts (ignored unless production build)
# These are generated by <PERSON><PERSON> Mix and copied to root
/js/
/css/
/fonts/
/mix-manifest.json

# Laravel Mix build outputs in src/public
/src/public/js/
/src/public/css/
/src/public/fonts/
/src/public/mix-manifest.json
/src/public/hot

# Development and build cache
/src/public/storage
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# === PRODUCTION MODE ===
# Build artifacts are tracked in production
# Only ignore development-specific files
/src/public/hot
*.map
.DS_Store
Thumbs.db


# === PRODUCTION MODE ===
# Build artifacts are tracked in production
# Only ignore development-specific files
/src/public/hot
*.map
.DS_Store
Thumbs.db


# === PRODUCTION MODE ===
# Build artifacts are tracked in production
# Only ignore development-specific files
/src/public/hot
*.map
.DS_Store
Thumbs.db
