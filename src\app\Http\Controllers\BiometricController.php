<?php
namespace App\Http\Controllers;

use App\Models\Core\Auth\User;
use App\Models\Tenant\Attendance\AttendanceDetails;
use App\Models\Tenant\BiometricDevice;
use App\Repositories\Core\Status\StatusRepository;
use App\Services\Tenant\Attendance\AttendanceService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BiometricController extends Controller
{
    public function handleCdata(Request $request)
    {
        $raw = file_get_contents('php://input');

        // Try to identify the device from request headers or IP
        $deviceId = $this->identifyDevice($request);

        // Only log if there's actual attendance data
        if (!empty(trim($raw))) {
            Log::info("ZKTeco attendance data received", [
                'device_id' => $deviceId,
                'records_count' => substr_count($raw, "\n")
            ]);
        }

        $lines = explode("\n", trim($raw));
        foreach ($lines as $line) {
            $line = trim($line);
            // Skip empty lines
            if (!$line) continue;

            $parts = explode("\t", $line);

            // Check if it's a standard attendance record (user ID + timestamp at minimum)
            if (count($parts) >= 4 && is_numeric($parts[0])) {
                $biometricUserId = $parts[0];
                $timestamp = $parts[1];
                $checkType = $parts[2] ?? null;
                $verifyMode = $parts[3] ?? null;

                // Removed detailed parsing log to reduce storage

                // Process the attendance data
                $this->processAttendanceData($biometricUserId, $timestamp, $checkType, $verifyMode, $deviceId);
            }
            // Removed ignored line log to reduce storage
        }

        return response('OK', 200);
    }

    /**
     * Try to identify the biometric device from request
     */
    private function identifyDevice(Request $request): ?string
    {
        // Method 1: From query parameter `SN` (ZKTeco standard)
        if ($request->has('SN')) {
            return $request->get('SN');
        }
    
        // Method 2: From `device_id` (custom client)
        if ($request->has('device_id')) {
            return $request->get('device_id');
        }
    
        // Method 3: From custom header
        if ($request->header('X-Device-ID')) {
            return $request->header('X-Device-ID');
        }
    
        // Method 4: From User-Agent string (rare)
        if (preg_match('/Device[_-]?ID[:\s]*([A-Za-z0-9\-_]+)/i', $request->userAgent(), $matches)) {
            return $matches[1];
        }
    
        // Method 5: From IP (if mapped)
        return null;
    }



    /**
     * Process attendance data from biometric device
     *
     * @param string $biometricUserId The user ID from biometric device
     * @param string $timestamp The timestamp from biometric device
     * @param string|null $checkType The check type (0: Check-In, 1: Check-Out, etc.)
     * @param string|null $verifyMode The verification mode
     * @param string|null $deviceId The device ID that sent the data
     * @return void
     */
    private function processAttendanceData($biometricUserId, $timestamp, $checkType = null, $verifyMode = null, $deviceId = null)
    {
        try {
            // Find the biometric device configuration
            $device = null;
            if ($deviceId) {
                $device = BiometricDevice::active()->byDeviceId($deviceId)->first();
                if (!$device) {
                    Log::warning("Device {$deviceId} not found");
                    return;
                }
            }

            // Find user by employee_id (which is mapped to biometric device user ID)
            // First try with the raw biometric user ID
            $user = User::query()->whereHas('profile', function ($query) use ($biometricUserId) {
                $query->where('employee_id', $biometricUserId);
            })->first();

            // If not found and biometric ID is numeric, try with EMP- prefix
            if (!$user && is_numeric($biometricUserId)) {
                $employeeIdWithPrefix = 'EMP-' . $biometricUserId;
                $user = User::query()->whereHas('profile', function ($query) use ($employeeIdWithPrefix) {
                    $query->where('employee_id', $employeeIdWithPrefix);
                })->first();

                // User found - removed log to reduce storage
            }

            if (!$user) {
                Log::warning("User ID {$biometricUserId} not found");
                return;
            }

            // Parse timestamp from biometric device with proper timezone handling
            // Biometric devices typically send time in their local timezone
            $appTimezone = settings('time_zone', config('app.timezone', 'UTC'));

            // Parse the timestamp assuming it's in the app's configured timezone
            $dateTime = Carbon::parse($timestamp, $appTimezone);

            // Convert to UTC for storage (Laravel standard)
            $dateTime = $dateTime->utc();

            // Timezone conversion completed - removed log to reduce storage

            // Get attendance service
            $attendanceService = resolve(AttendanceService::class);

            // Get approved status
            $statusApproved = resolve(StatusRepository::class)->attendanceApprove();

            // Determine if this is a punch in or punch out based on device mode and check type
            $isPunchIn = $this->determinePunchType($device, $checkType, $user->id);

            // Validate device mode compatibility
            if ($device) {
                if ($isPunchIn && !$device->supportsPunchIn()) {
                    Log::warning("Device {$device->device_id} doesn't support punch in");
                    return;
                }

                if (!$isPunchIn && !$device->supportsPunchOut()) {
                    Log::warning("Device {$device->device_id} doesn't support punch out");
                    return;
                }
            }

            // Additional validation for automatic mode
            if ($device && $device->mode === 'automatic') {
                $this->validateAutomaticModeLogic($user->id, $isPunchIn, $dateTime);
            }

            DB::transaction(function () use ($user, $dateTime, $statusApproved, $attendanceService, $isPunchIn, $verifyMode, $device, $deviceId) {
                // Prepare IP data
                $ipData = [
                    'ip' => request()->ip(),
                    'source' => 'Biometric Device',
                    'device_id' => $deviceId,
                    'device_name' => $device ? $device->device_name : 'Unknown Device',
                    'device_mode' => $device ? $device->mode : 'unknown',
                    'verify_mode' => $verifyMode
                ];

                if ($isPunchIn) {
                    // Handle punch in
                    $attendanceService
                        ->setModel($user)
                        ->setAttributes([
                            'status_id' => $statusApproved,
                            'punch_in' => true,
                            'now' => $dateTime->format('Y-m-d H:i:s'),
                            'today' => $dateTime->format('Y-m-d'),
                            'ip_data' => $ipData,
                            'note' => 'Attendance recorded via biometric device',
                            'note_user_id' => $user->id  // Set the user_id for the note
                        ])
                        ->punchIn();

                    Log::info("Biometric punch in: {$user->full_name} at {$dateTime->format('H:i:s')}");
                } else {
                    // Handle punch out - but first check if user has an active punch in
                    $unpunchedRecord = AttendanceDetails::getUnPunchedOut($user->id);

                    if (!$unpunchedRecord) {
                        // User trying to punch out without punch in
                        Log::warning("User {$user->id} punch out without punch in");

                        // For punch_out_only devices, we can either:
                        // 1. Skip this punch (current approach)
                        // 2. Force a punch in first, then punch out
                        // 3. Create a punch in/out pair for the same time

                        if ($device && $device->mode === 'punch_out_only') {
                            // For punch_out_only devices, create a punch in/out pair
                            // This maintains audit trail while handling the edge case
                            Log::info("Creating punch in/out pair for punch_out_only device", [
                                'user_id' => $user->id,
                                'device_id' => $device->device_id,
                                'reason' => 'no_active_punch_in'
                            ]);

                            // First create punch in (1 minute before punch out)
                            $punchInTime = $dateTime->copy()->subMinute();
                            $attendanceService
                                ->setModel($user)
                                ->setAttributes([
                                    'status_id' => $statusApproved,
                                    'punch_in' => true,
                                    'now' => $punchInTime->format('Y-m-d H:i:s'),
                                    'today' => $punchInTime->format('Y-m-d'),
                                    'ip_data' => $ipData,
                                    'note' => 'Auto punch in created for biometric punch out',
                                    'note_user_id' => $user->id
                                ])
                                ->punchIn();

                            // Then proceed with punch out below
                        } else {
                            // For automatic mode, skip the punch
                            Log::info("Skipping punch out - no active punch in found", [
                                'user_id' => $user->id
                            ]);
                            return;
                        }
                    }

                    // User has active punch in, proceed with punch out
                    $attendanceService
                        ->setModel($user)
                        ->setAttributes([
                            'status_id' => $statusApproved,
                            'punch_in' => false,
                            'now' => $dateTime->format('Y-m-d H:i:s'),
                            'ip_data' => $ipData,
                            'note' => 'Attendance recorded via biometric device',
                            'note_user_id' => $user->id  // Set the user_id for the note
                        ])
                        ->punchOut();

                    Log::info("Biometric punch out: {$user->full_name} at {$dateTime->format('H:i:s')}");
                }
            });
        } catch (\Exception $e) {
            Log::error("Error processing biometric attendance data", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'biometric_user_id' => $biometricUserId,
                'timestamp' => $timestamp
            ]);
        }
    }

    /**
     * Determine punch type based on device mode, check type, and user status
     */
    private function determinePunchType(?BiometricDevice $device, ?string $checkType, int $userId): bool
    {
        $deviceMode = $device ? $device->mode : 'unknown';

        Log::info("Determining punch type", [
            'user_id' => $userId,
            'device_mode' => $deviceMode,
            'check_type' => $checkType,
            'device_id' => $device ? $device->device_id : null
        ]);

        // If device has a specific mode (not automatic), use that
        if ($device) {
            switch ($device->mode) {
                case 'punch_in_only':
                    return true; // Always punch in

                case 'punch_out_only':
                    return false; // Always punch out

                case 'automatic':
                    // Continue with automatic detection logic below
                    break;

                default:
                    // Unknown device mode, using automatic detection
                    break;
            }
        }

        // If check type is provided by the device, use it to determine punch type
        // Standard ZKTeco check types: 0 = Check-In, 1 = Check-Out, 2 = Break-Out, 3 = Break-In, etc.
        if ($checkType !== null) {
            // Handle known check types
            if (in_array($checkType, ['0', '1', '2', '3'])) {
                $isPunchIn = $checkType == '0';
                Log::info("Using device check type to determine punch", [
                    'check_type' => $checkType,
                    'determined_action' => $isPunchIn ? 'punch_in' : 'punch_out',
                    'user_id' => $userId
                ]);
                return $isPunchIn;
            } else {
                // Handle unknown/invalid check types (like 255)
                // Unknown check type - using automatic detection

                // Fall through to automatic detection logic below
            }
        }

        // Automatic detection: check if user already has an unpunched out record for today
        $unpunchedRecord = AttendanceDetails::getUnPunchedOut($userId);
        $isPunchIn = !$unpunchedRecord;

        // Automatic detection completed - removed log to reduce storage

        return $isPunchIn; // If no unpunched record, it's a punch in
    }

    /**
     * Additional validation for automatic mode to prevent logical errors
     */
    private function validateAutomaticModeLogic(int $userId, bool $isPunchIn, Carbon $dateTime): void
    {
        $today = $dateTime->format('Y-m-d');

        if ($isPunchIn) {
            // Check if user already has a punch in for today without punch out
            $existingPunchIn = AttendanceDetails::whereHas('attendance', function ($query) use ($userId, $today) {
                $query->where('user_id', $userId)
                      ->whereDate('in_date', $today);
            })
            ->whereNotNull('in_time')
            ->whereNull('out_time')
            ->first();

            if ($existingPunchIn) {
                Log::warning("User {$userId} already punched in today");
            }
        } else {
            // Check if user has a punch in to punch out from
            $punchInRecord = AttendanceDetails::getUnPunchedOut($userId);

            if (!$punchInRecord) {
                Log::warning("User {$userId} punch out without active punch in");
            } else {
                Log::info("Valid punch out - found matching punch in", [
                    'user_id' => $userId,
                    'punch_in_time' => $punchInRecord->in_time,
                    'attendance_detail_id' => $punchInRecord->id
                ]);
            }
        }
    }

    public function getRequest(Request $request)
    {
        // Only log device info updates, not heartbeat requests
        if ($request->has('INFO')) {
            Log::info("ZKTeco device info", [
                'device_id' => $request->get('SN'),
                'info' => $request->get('INFO')
            ]);
        }
        return response('OK', 200);
    }

    public function deviceCmd(Request $request)
    {
        // Device command endpoint - no logging to reduce storage
        return response('', 200); // No commands issued
    }
}
