<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>All Employees For Signature PDF</title>
    <link rel="stylesheet" href="{{ url('css/payslip.css') }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
        }
        .signature-header {
            text-align: center;
            margin-bottom: 30px;
            font-size: 10px;
        }
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 10px;
        }
        .signature-table th,
        .signature-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }
        .signature-table th {
            font-weight: bold;
            text-align: center;
            font-size: 10px;
        }
        .payroll-info {
            margin-bottom: 20px;
            font-size: 10px;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
            font-size: 10px;
        }
        .signature-box {
            width: 45%;
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-top: 50px;
            font-size: 10px;
        }
        .approval-section {
            margin-top: 30px;
            padding: 15px;
            font-size: 10px;
            border: 1px solid #000;
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: Arial, sans-serif;
            font-size: 10px;
        }
        p {
            font-size: 10px;
        }
    </style>
</head>
<body>
<div>
    <div class="signature-header">
        <img src="{{ property_exists($settings, 'tenant_logo') ? asset($settings->tenant_logo) : asset('images/logo/default-logo.png') }}"
             alt="logo"
             class="img-fluid mb-2"
             style="max-height: 70px; max-width: 70px;"
        />
        <h3 class="font-weight-bold" style="font-size: 13px;">{{ $settings->tenant_name ?? 'Company Name' }}</h3>
        @php
            $addressParts = [];
            if (property_exists($settings, 'address') && !empty($settings->address) && $settings->address !== 'null') {
                $addressParts[] = $settings->address;
            }
            if (property_exists($settings, 'area') && !empty($settings->area) && $settings->area !== 'null') {
                $addressParts[] = $settings->area;
            }
            if (property_exists($settings, 'city') && !empty($settings->city) && $settings->city !== 'null') {
                $addressParts[] = $settings->city;
            }
        @endphp

        @if(!empty($addressParts))
            <p class="mb-2">
                {{ implode(', ', $addressParts) }}
            </p>
        @endif
        <h4 style="font-size: 13px;">ALL EMPLOYEES PAYROLL FOR SIGNATURE</h4>
        @php
            $timezone = auth()->user()->timezone ?? settings('time_zone', 'UTC');
            $currentDateTime = now()->setTimezone($timezone);
        @endphp
        <p>Generated on: {{ $currentDateTime->format('F d, Y') }} at {{ $currentDateTime->format('h:i:s A') }}</p>
    </div>

    @php
        // Get unique employees from payslips
        $uniqueEmployees = collect();
        $totalSalary = 0;

        // Get payroll period from payslips or ranges
        $payrollPeriodText = 'N/A';
        if ($payslips->isNotEmpty()) {
            $startDate = $payslips->min('start_date');
            $endDate = $payslips->max('end_date');

            if ($startDate && $endDate) {
                $startFormatted = \Carbon\Carbon::parse($startDate)->format('M d');
                $endFormatted = \Carbon\Carbon::parse($endDate)->format('M d, Y');
                $payrollPeriodText = $startFormatted . '-' . $endFormatted;
            }
        } elseif (isset($ranges) && count($ranges) >= 2) {
            $startFormatted = \Carbon\Carbon::parse($ranges[0])->format('M d');
            $endFormatted = \Carbon\Carbon::parse($ranges[1])->format('M d, Y');
            $payrollPeriodText = $startFormatted . '-' . $endFormatted;
        }

        foreach($payslips as $payslip) {
            if (!$uniqueEmployees->contains('id', $payslip->user->id)) {
                // Load profile and department relationships if not already loaded
                $payslip->user->load(['profile', 'department', 'designation']);
                $uniqueEmployees->push($payslip->user);
            }
        }
    @endphp

    <table class="signature-table">
        <thead>
            <tr>
                <th colspan="5" style="text-align: center; padding: 10px; font-size: 13px; font-weight: bold;">PAYROLL COVERED AS OF {{ $payrollPeriodText }}</th>
            </tr>
            <tr>
                <th style="width: 12%; text-align: center;">EMPLOYEE<br>ID</th>
                <th style="width: 30%; text-align: center;">NAME</th>
                <th style="width: 20%; text-align: center;">DEPARTMENT</th>
                <th style="width: 23%; text-align: center;">DESIGNATION</th>
                <th style="width: 15%; text-align: center;">SIGNATURE</th>
            </tr>
        </thead>
        <tbody>
            @foreach($uniqueEmployees as $emp)
                @php
                    // Get the employee ID from profile, fallback to user ID if not available
                    $employeeId = $emp->profile->employee_id ?? $emp->id ?? 'N/A';
                    $employeeName = $emp->full_name;
                    $department = $emp->department->name ?? 'N/A';
                    $designation = $emp->designation->name ?? 'N/A';

                    // Calculate total salary from payslips for this employee
                    $employeePayslips = $payslips->where('user_id', $emp->id);
                    $salary = 0;
                    foreach($employeePayslips as $payslip) {
                        // Use the already calculated net salary from the system
                        // For signature, we typically show the total salary (net pay)
                        $salary += $payslip->net_salary;
                    }

                    $totalSalary += $salary;
                @endphp
                <tr>
                    <td style="text-align: center;">{{ $employeeId }}</td>
                    <td>{{ $employeeName }}</td>
                    <td>{{ $department }}</td>
                    <td>{{ $designation }}</td>
                    <td style="height: 40px; border-bottom: 1px solid #000; text-align: center;"></td>
                </tr>
            @endforeach
        </tbody>
    </table>



    <div style="margin-top: 20px; text-align: center;">
        <p>Prepared by:</p>
       <p><strong>{{ auth()->user()->full_name ?? 'System Administrator' }}</strong></p>
        <p>Payroll Analyst</p>
    </div>
</div>
</body>
</html>
